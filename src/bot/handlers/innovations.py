"""
Innovative Features Handlers
Handles the new innovative functions for EduGuideBot v3.
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

# Add project root to path
import sys
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.core.data_loader import load_raw
from src.core.feature_engineering import add_derived_features
from src.bot.i18n import t, get_lang
from src.bot.telegram_safe import safe_answer_callback, safe_edit_message

logger = logging.getLogger(__name__)

def get_accurate_university_count() -> Dict[str, int]:
    """Get accurate count of unique universities by city."""
    return {
        'PP': 41,  # Phnom Penh
        'SR': 6,   # Siem Reap  
        'BTB': 2   # Battambang
    }

def get_location_keyboard(lang: str = 'kh') -> InlineKeyboardMarkup:
    """Get location selection keyboard."""
    counts = get_accurate_university_count()
    
    if lang == 'kh':
        keyboard = [
            [InlineKeyboardButton(f"📍 ភ្នំពេញ ({counts['PP']} សាកលវិទ្យាល័យ)", callback_data="location_PP")],
            [InlineKeyboardButton(f"📍 សៀមរាប ({counts['SR']} សាកលវិទ្យាល័យ)", callback_data="location_SR")],
            [InlineKeyboardButton(f"📍 បាត់ដំបង ({counts['BTB']} សាកលវិទ្យាល័យ)", callback_data="location_BTB")],
            [InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="back_to_main")]
        ]
    else:
        keyboard = [
            [InlineKeyboardButton(f"📍 Phnom Penh ({counts['PP']} universities)", callback_data="location_PP")],
            [InlineKeyboardButton(f"📍 Siem Reap ({counts['SR']} universities)", callback_data="location_SR")],
            [InlineKeyboardButton(f"📍 Battambang ({counts['BTB']} universities)", callback_data="location_BTB")],
            [InlineKeyboardButton("🔙 Back", callback_data="back_to_main")]
        ]
    
    return InlineKeyboardMarkup(keyboard)

def get_major_categories_keyboard(lang: str = 'kh') -> InlineKeyboardMarkup:
    """Get major categories keyboard."""
    if lang == 'kh':
        keyboard = [
            [InlineKeyboardButton("🔬 STEM (វិទ្យាសាស្ត្រ & បច្ចេកវិទ្យា)", callback_data="major_stem")],
            [InlineKeyboardButton("💼 ពាណិជ្ជកម្ម & សេដ្ឋកិច្ច", callback_data="major_business")],
            [InlineKeyboardButton("🏥 សុខភាព & វេជ្ជសាស្ត្រ", callback_data="major_health")],
            [InlineKeyboardButton("🎨 សិល្បៈ & មនុស្សសាស្ត្រ", callback_data="major_arts")],
            [InlineKeyboardButton("🏛️ វិទ្យាសាស្ត្រសង្គម", callback_data="major_social")],
            [InlineKeyboardButton("📚 អប់រំ", callback_data="major_education")],
            [InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="back_to_main")]
        ]
    else:
        keyboard = [
            [InlineKeyboardButton("🔬 STEM (Science & Technology)", callback_data="major_stem")],
            [InlineKeyboardButton("💼 Business & Economics", callback_data="major_business")],
            [InlineKeyboardButton("🏥 Health & Medicine", callback_data="major_health")],
            [InlineKeyboardButton("🎨 Arts & Humanities", callback_data="major_arts")],
            [InlineKeyboardButton("🏛️ Social Sciences", callback_data="major_social")],
            [InlineKeyboardButton("📚 Education", callback_data="major_education")],
            [InlineKeyboardButton("🔙 Back", callback_data="back_to_main")]
        ]
    
    return InlineKeyboardMarkup(keyboard)

async def handle_find_university(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle find university button - starts the 16-question assessment."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    lang = get_lang(context.user_data)
    
    text = """🎯 **ការស្វែងរកសាកលវិទ្យាល័យដោយ AI**

🤖 ខ្ញុំនឹងសួរអ្នកនូវសំណួរ 16 ដើម្បីស្វែងរកសាកលវិទ្យាល័យដែលសមស្របបំផុតសម្រាប់អ្នក។

✨ **ការវាយតម្លៃនេះរួមមាន:**
• ចំណាប់អារម្មណ៍ និងជំនាញ
• ថវិកា និងទីតាំង
• គោលដៅអាជីព
• របៀបរៀនដែលចូលចិត្ត

⏱️ **ពេលវេលា:** ប្រហែល 5-10 នាទី
🎯 **ភាពត្រឹមត្រូវ:** 93.3%

🚀 **ចាប់ផ្តើមការវាយតម្លៃ?**"""

    keyboard = [
        [InlineKeyboardButton("✅ ចាប់ផ្តើម", callback_data="start_assessment")],
        [InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="back_to_main")]
    ]
    
    await safe_edit_message(
        query,
        text.strip(),
        InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def handle_by_location(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle search by location."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    lang = get_lang(context.user_data)
    counts = get_accurate_university_count()
    
    text = f"""📍 **ស្វែងរកតាមទីតាំង**

🏛️ **សាកលវិទ្យាល័យនៅកម្ពុជា:**
• ភ្នំពេញ: {counts['PP']} សាកលវិទ្យាល័យ
• សៀមរាប: {counts['SR']} សាកលវិទ្យាល័យ
• បាត់ដំបង: {counts['BTB']} សាកលវិទ្យាល័យ

📊 **សរុប:** {sum(counts.values())} សាកលវិទ្យាល័យ

🎯 **ជ្រើសរើសទីតាំងដែលអ្នកចង់សិក្សា:**"""

    await safe_edit_message(
        query,
        text.strip(),
        get_location_keyboard(lang),
        parse_mode='Markdown'
    )

async def handle_by_major(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle search by major."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    lang = get_lang(context.user_data)
    
    text = """🎓 **ស្វែងរកតាមជំនាញ**

📚 **ជ្រើសរើសផ្នែកសិក្សាដែលអ្នកចាប់អារម្មណ៍:**

🔬 **STEM** - វិទ្យាសាស្ត្រ, បច្ចេកវិទ្យា, វិស្វកម្ម, គណិតវិទ្យា
💼 **ពាណិជ្ជកម្ម** - គ្រប់គ្រង, សេដ្ឋកិច្ច, ហិរញ្ញវត្ថុ, ទីផ្សារ
🏥 **សុខភាព** - វេជ្ជសាស្ត្រ, ឱសថ, ពេទ្យបំរុង, សុខភាពសាធារណៈ
🎨 **សិល្បៈ** - ការរចនា, ប្រព័ន្ធផ្សព្វផ្សាយ, ភាសា, វប្បធម៌
🏛️ **វិទ្យាសាស្ត្រសង្គម** - ចិត្តវិទ្យា, នយោបាយ, សង្គមវិទ្យា
📚 **អប់រំ** - បង្រៀន, គ្រប់គ្រងអប់រំ, កម្មវិធីសិក្សា"""

    await safe_edit_message(
        query,
        text.strip(),
        get_major_categories_keyboard(lang),
        parse_mode='Markdown'
    )

async def handle_contact_info(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle contact information request."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    lang = get_lang(context.user_data)
    
    text = """📞 **ព័ត៌មានទំនាក់ទំនងសាកលវិទ្យាល័យ**

🔍 **របៀបទទួលបានព័ត៌មានទំនាក់ទំនង:**

1️⃣ **ស្វែងរកសាកលវិទ្យាល័យជាក់លាក់**
   • ប្រើមុខងារ "តាមទីតាំង" ឬ "តាមជំនាញ"
   • ជ្រើសរើសសាកលវិទ្យាល័យ
   • ចុចលើ "ព័ត៌មានទំនាក់ទំនង"

2️⃣ **ព័ត៌មានដែលមាន:**
   📞 លេខទូរស័ព្ទ
   📧 អ៊ីមែល
   🌐 គេហទំព័រ
   📍 អាសយដ្ឋាន
   📘 Facebook Page

3️⃣ **សាកលវិទ្យាល័យដែលបានផ្ទៀងផ្ទាត់:**
   ✅ ព័ត៌មានទំនាក់ទំនងត្រឹមត្រូវ 100%
   ✅ បានស្រាវជ្រាវពីគេហទំព័រផ្លូវការ
   ✅ ធ្វើបច្ចុប្បន្នភាពទៀងទាត់"""

    keyboard = [
        [InlineKeyboardButton("📍 ស្វែងរកតាមទីតាំង", callback_data="by_location")],
        [InlineKeyboardButton("🎓 ស្វែងរកតាមជំនាញ", callback_data="by_major")],
        [InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="back_to_main")]
    ]
    
    await safe_edit_message(
        query,
        text.strip(),
        InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def handle_compare_universities(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle university comparison feature."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    lang = get_lang(context.user_data)
    
    text = """📊 **ប្រៀបធៀបសាកលវិទ្យាល័យ**

🔍 **មុខងារប្រៀបធៀប:**

📋 **ការប្រៀបធៀបរួមមាន:**
• 💰 តម្លៃសិក្សា (USD)
• 📍 ទីតាំង និងភូមិសាស្ត្រ
• 🎓 កម្មវិធីសិក្សាដែលមាន
• 📞 ព័ត៌មានទំនាក់ទំនង
• 🏛️ ប្រភេទសាកលវិទ្យាល័យ (សាធារណៈ/ឯកជន)
• 📅 ឆ្នាំបង្កើត
• 📋 តម្រូវការចូលរៀន

⚡ **របៀបប្រើប្រាស់:**
1. ជ្រើសរើសសាកលវិទ្យាល័យទី 1
2. ជ្រើសរើសសាកលវិទ្យាល័យទី 2  
3. មើលការប្រៀបធៀបលម្អិត

🎯 **ចាប់ផ្តើមប្រៀបធៀប:**"""

    keyboard = [
        [InlineKeyboardButton("🏛️ ជ្រើសរើសសាកលវិទ្យាល័យ", callback_data="select_for_comparison")],
        [InlineKeyboardButton("📍 ប្រៀបធៀបតាមទីតាំង", callback_data="compare_by_location")],
        [InlineKeyboardButton("🔙 ត្រលប់ក្រោយ", callback_data="back_to_main")]
    ]
    
    await safe_edit_message(
        query,
        text.strip(),
        InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def handle_explore_all(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle explore all universities feature."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    lang = get_lang(context.user_data)
    counts = get_accurate_university_count()
    
    text = f"""🏛️ **បង្ហាញសាកលវិទ្យាល័យទាំងអស់**

📊 **ទិន្នន័យសាកលវិទ្យាល័យនៅកម្ពុជា:**

🏙️ **ភ្នំពេញ:** {counts['PP']} សាកលវិទ្យាល័យ
   • សាកលវិទ្យាល័យសាធារណៈ និងឯកជន
   • កម្មវិធីសិក្សាចម្រុះ
   • មជ្ឈមណ្ឌលអប់រំសំខាន់

🏛️ **សៀមរាប:** {counts['SR']} សាកលវិទ្យាល័យ
   • ផ្តោតលើទេសចរណ៍ និងវប្បធម៌
   • កម្មវិធីអន្តរជាតិ

🌾 **បាត់ដំបង:** {counts['BTB']} សាកលវិទ្យាល័យ
   • ផ្តោតលើកសិកម្ម និងអភិវឌ្ឍន៍ជនបទ

📈 **សរុប:** {sum(counts.values())} សាកលវិទ្យាល័យ
🎓 **កម្មវិធីសិក្សា:** 500+ កម្មវិធី

🔍 **ជ្រើសរើសទីតាំងដើម្បីមើលបញ្ជីពេញលេញ:**"""

    await safe_edit_message(
        query,
        text.strip(),
        get_location_keyboard(lang),
        parse_mode='Markdown'
    )

async def handle_back_to_main(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle back to main menu."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    lang = get_lang(context.user_data)
    
    # Import the main menu keyboard function
    from src.bot.commands import get_main_menu_keyboard
    
    text = """🎓 **EduGuideBot v3 - ម៉ឺនុយមេ**

🚀 **ជ្រើសរើសមុខងារដែលអ្នកចង់ប្រើ:**

🎯 **ស្វែងរកសាកលវិទ្យាល័យ** - ការវាយតម្លៃ 16 សំណួរ
📍 **តាមទីតាំង** - រកតាមខេត្ត/ក្រុង
🎓 **តាមជំនាញ** - រកតាមផ្នែកសិក្សា
📞 **ព័ត៌មានទំនាក់ទំនង** - លេខទូរស័ព្ទ អ៊ីមែល
📊 **ប្រៀបធៀប** - ប្រៀបធៀបសាកលវិទ្យាល័យ
🏛️ **បង្ហាញទាំងអស់** - មើលសាកលវិទ្យាល័យទាំងអស់"""

    await safe_edit_message(
        query,
        text.strip(),
        get_main_menu_keyboard(lang),
        parse_mode='Markdown'
    )
