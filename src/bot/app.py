"""
EduGuideBot v3 Application
Main bot application setup and configuration.
"""

import logging
from telegram.ext import <PERSON><PERSON>uilder, CommandHandler, CallbackQueryHandler
from .handlers.assessment import (
    handle_language_selection,
    handle_assessment_answer,
    start_assessment_flow,
    handle_back_to_question,
    handle_back_to_main_menu
)
from .handlers.recommendations import show_recommendations, show_recommendation_reason

from .handlers.details import (
    show_major_details,
    show_university_location,
    show_university_contact,
    show_university_requirements,
    generate_pdf_export,
    back_to_recommendations
)
from .commands_v3 import ai_status_command as ai_status, ai_debug_command as ai_debug

logger = logging.getLogger(__name__)


async def create_bot_application(token: str):
    """Create and configure the Telegram bot application"""
    try:
        application = ApplicationBuilder().token(token).build()

        # Register command handlers
        from .commands_v3 import start_command
        application.add_handler(CommandHandler("start", start_command))
        application.add_handler(CommandHandler("recommend", show_recommendations))
        application.add_handler(CommandHandler("status", ai_status))
        application.add_handler(CommandHandler("debug", ai_debug))

        # Register callback handlers with CORRECT patterns
        application.add_handler(CallbackQueryHandler(handle_language_selection, pattern=r"^lang_"))
        application.add_handler(CallbackQueryHandler(start_assessment_flow, pattern=r"^start_assessment$"))
        application.add_handler(CallbackQueryHandler(handle_assessment_answer, pattern=r"^ans_\d+_\d+$"))

        # Back navigation handlers
        application.add_handler(CallbackQueryHandler(handle_back_to_question, pattern=r"^back_to_question_"))
        application.add_handler(CallbackQueryHandler(handle_back_to_main_menu, pattern=r"^back_to_main_menu$"))

        application.add_handler(CallbackQueryHandler(show_recommendations, pattern=r"^QS_\w+$"))
        application.add_handler(CallbackQueryHandler(show_major_details, pattern=r"^details_"))
        application.add_handler(CallbackQueryHandler(show_recommendation_reason, pattern=r"^reason_"))
        application.add_handler(CallbackQueryHandler(show_university_location, pattern=r"^location_"))
        application.add_handler(CallbackQueryHandler(show_university_contact, pattern=r"^contact_"))
        application.add_handler(CallbackQueryHandler(show_university_requirements, pattern=r"^requirements_"))
        application.add_handler(CallbackQueryHandler(generate_pdf_export, pattern=r"^pdf_export_"))
        application.add_handler(CallbackQueryHandler(back_to_recommendations, pattern=r"^(back_to_recommendations|BACK)$"))



        # Log handler count for debugging
        total_handlers = sum(len(handlers) for handlers in application.handlers.values())
        logger.info(f"EduGuideBot v3 application configured successfully")
        logger.info(f"Total handlers registered: {total_handlers}")

        # Count specific handler types
        command_count = 0
        callback_count = 0
        for group_handlers in application.handlers.values():
            for handler in group_handlers:
                if hasattr(handler, 'command'):
                    command_count += 1
                elif hasattr(handler, 'pattern'):
                    callback_count += 1

        logger.info(f"Command handlers: {command_count}")
        logger.info(f"Callback handlers: {callback_count}")

        return application

    except Exception as e:
        logger.error(f"Failed to create bot application: {e}")
        raise



